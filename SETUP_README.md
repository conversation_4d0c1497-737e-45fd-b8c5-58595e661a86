# Odoo Project Setup and Run Scripts

Este projeto inclui scripts para facilitar a configuração e execução do Odoo.

## Scripts Disponíveis

### 1. `run_odoo.sh` - Script Completo de Setup e Execução

Script principal que configura todo o ambiente e executa o Odoo.

**Uso:**
```bash
# Configurar e executar (primeira vez)
./run_odoo.sh

# Apenas configurar o ambiente
./run_odoo.sh --setup-only

# Pular configuração e executar diretamente
./run_odoo.sh --skip-setup

# Mostrar ajuda
./run_odoo.sh --help
```

**O que o script faz:**
- ✅ Verifica e configura PostgreSQL
- ✅ Cria usuário e banco de dados do Odoo
- ✅ Instala dependências Python
- ✅ Executa o servidor Odoo

### 2. `dev_run.sh` - Script Rápido para Desenvolvimento

Script simplificado para desenvolvimento, assumindo que o ambiente já está configurado.

**Uso:**
```bash
./dev_run.sh
```

**Características:**
- 🚀 Início rápido
- 🔄 Auto-reload em desenvolvimento
- 🐛 Log level debug
- 📝 Inclui dados de demonstração

## Pré-requisitos

### PostgreSQL
**macOS:**
```bash
brew install postgresql
brew services start postgresql
```

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install postgresql postgresql-contrib
sudo systemctl start postgresql
```

**CentOS/RHEL:**
```bash
sudo yum install postgresql-server postgresql-contrib
sudo systemctl start postgresql
```

### Python 3.8+
**macOS:**
```bash
brew install python3
```

**Ubuntu/Debian:**
```bash
sudo apt-get install python3 python3-pip
```

## Configuração Manual (se necessário)

### 1. PostgreSQL
```bash
# Criar usuário
sudo -u postgres createuser -s odoo

# Definir senha
sudo -u postgres psql -c "ALTER USER odoo PASSWORD 'odoo';"
```

### 2. Python Dependencies
```bash
pip3 install -r requirements.txt
```

### 3. Executar Odoo
```bash
python3 odoo-bin -c odoo.conf
```

## Configuração do Odoo

O arquivo `odoo.conf` contém as configurações principais:

- **Database:** localhost:5432
- **User:** odoo
- **Password:** odoo
- **Port:** 8069
- **Admin Password:** admin123

## Acesso ao Sistema

Após executar qualquer script:

1. Abra o navegador em: http://localhost:8069
2. Primeira vez: criar banco de dados
3. Login admin com senha: admin123

## Troubleshooting

### PostgreSQL não inicia
```bash
# macOS
brew services restart postgresql

# Linux
sudo systemctl restart postgresql
```

### Erro de permissões Python
```bash
# Usar virtual environment
python3 -m venv odoo-env
source odoo-env/bin/activate
pip install -r requirements.txt
```

### Porta 8069 em uso
```bash
# Verificar processo usando a porta
lsof -i :8069

# Matar processo se necessário
kill -9 <PID>
```

### Erro de dependências
```bash
# Reinstalar dependências
pip3 install --upgrade -r requirements.txt
```

## Desenvolvimento

Para desenvolvimento, use o `dev_run.sh` que inclui:

- **Auto-reload:** Reinicia automaticamente quando arquivos mudam
- **Debug mode:** Logs detalhados
- **QWeb debug:** Debug de templates
- **Werkzeug debugger:** Debug de Python

## Logs

Os logs são exibidos no terminal. Para logs em arquivo, modifique `odoo.conf`:

```ini
logfile = /var/log/odoo/odoo.log
log_level = info
```

## Backup do Banco

```bash
# Backup
pg_dump -h localhost -U odoo -d odoo > backup.sql

# Restore
psql -h localhost -U odoo -d odoo < backup.sql
```

## Addons Customizados

Para adicionar addons customizados:

1. Crie pasta: `custom_addons/`
2. Modifique `odoo.conf`:
   ```ini
   addons_path = ./addons,./odoo/addons,./custom_addons
   ```

## Suporte

Para problemas específicos:

1. Verifique logs no terminal
2. Consulte documentação oficial: https://www.odoo.com/documentation
3. Verifique configurações em `odoo.conf`
