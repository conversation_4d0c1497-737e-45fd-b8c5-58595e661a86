#!/bin/bash

# Quick Odoo Development Runner
# Simple script for development environment

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "odoo-bin" ]; then
    print_error "odoo-bin not found. Please run from Odoo project root."
    exit 1
fi

# Make odoo-bin executable
chmod +x odoo-bin

print_info "Starting Odoo in development mode..."

# Check if config file exists
if [ -f "odoo.conf" ]; then
    print_info "Using configuration file: odoo.conf"
    CONFIG_OPTION="-c odoo.conf"
else
    print_info "No config file found, using default settings"
    CONFIG_OPTION=""
fi

print_success "Odoo starting at http://localhost:8069"
print_info "Press Ctrl+C to stop"
print_info "Default admin password: admin123"

# Run Odoo with development options
python3 odoo-bin $CONFIG_OPTION \
    --dev=reload,qweb,werkzeug,xml \
    --log-level=debug \
    --without-demo=False
