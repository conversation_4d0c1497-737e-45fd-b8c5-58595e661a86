#!/bin/bash

# Odoo Project Runner Script
# This script sets up and runs the Odoo project

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ODOO_USER="odoo"
ODOO_DB="odoo"
ODOO_PASSWORD="odoo"
POSTGRES_PORT="5432"
ODOO_PORT="8069"
PYTHON_VERSION="python3"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if PostgreSQL is running
check_postgres() {
    if command_exists psql; then
        if pg_isready -h localhost -p $POSTGRES_PORT >/dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# Function to setup PostgreSQL
setup_postgres() {
    print_status "Setting up PostgreSQL..."
    
    if ! command_exists psql; then
        print_error "PostgreSQL is not installed. Please install it first:"
        echo "  macOS: brew install postgresql"
        echo "  Ubuntu/Debian: sudo apt-get install postgresql postgresql-contrib"
        echo "  CentOS/RHEL: sudo yum install postgresql-server postgresql-contrib"
        exit 1
    fi
    
    if ! check_postgres; then
        print_status "Starting PostgreSQL service..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            brew services start postgresql || {
                print_error "Failed to start PostgreSQL. Try: brew services start postgresql"
                exit 1
            }
        elif command_exists systemctl; then
            # Linux with systemd
            sudo systemctl start postgresql || {
                print_error "Failed to start PostgreSQL. Try: sudo systemctl start postgresql"
                exit 1
            }
        else
            print_error "Please start PostgreSQL manually"
            exit 1
        fi
        
        # Wait for PostgreSQL to start
        sleep 3
        
        if ! check_postgres; then
            print_error "PostgreSQL failed to start"
            exit 1
        fi
    fi
    
    print_success "PostgreSQL is running"
    
    # Create Odoo user and database
    print_status "Setting up Odoo database user and database..."
    
    # Check if user exists
    if ! sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$ODOO_USER'" | grep -q 1; then
        print_status "Creating PostgreSQL user: $ODOO_USER"
        sudo -u postgres createuser -s $ODOO_USER 2>/dev/null || {
            print_warning "User $ODOO_USER might already exist or creation failed"
        }
    else
        print_status "PostgreSQL user $ODOO_USER already exists"
    fi
    
    # Set password for user
    sudo -u postgres psql -c "ALTER USER $ODOO_USER PASSWORD '$ODOO_PASSWORD';" 2>/dev/null || {
        print_warning "Failed to set password for user $ODOO_USER"
    }
    
    print_success "PostgreSQL setup completed"
}

# Function to setup Python environment
setup_python() {
    print_status "Setting up Python environment..."
    
    if ! command_exists $PYTHON_VERSION; then
        print_error "Python 3 is not installed. Please install Python 3.8 or higher"
        exit 1
    fi
    
    # Check Python version
    python_version=$($PYTHON_VERSION --version 2>&1 | awk '{print $2}')
    print_status "Using Python version: $python_version"
    
    # Check if pip is available
    if ! command_exists pip3; then
        print_error "pip3 is not installed. Please install pip3"
        exit 1
    fi
    
    # Install/upgrade pip
    print_status "Upgrading pip..."
    $PYTHON_VERSION -m pip install --upgrade pip
    
    # Install requirements
    if [ -f "requirements.txt" ]; then
        print_status "Installing Python dependencies..."
        $PYTHON_VERSION -m pip install -r requirements.txt
        print_success "Python dependencies installed"
    else
        print_warning "requirements.txt not found"
    fi
}

# Function to check Odoo configuration
check_config() {
    print_status "Checking Odoo configuration..."
    
    if [ ! -f "odoo.conf" ]; then
        print_warning "odoo.conf not found, using default configuration"
        return 1
    fi
    
    print_success "Configuration file found: odoo.conf"
    return 0
}

# Function to run Odoo
run_odoo() {
    print_status "Starting Odoo server..."
    
    # Check if odoo-bin exists
    if [ ! -f "odoo-bin" ]; then
        print_error "odoo-bin not found in current directory"
        exit 1
    fi
    
    # Make odoo-bin executable
    chmod +x odoo-bin
    
    # Prepare command
    cmd="$PYTHON_VERSION odoo-bin"
    
    # Add config file if exists
    if [ -f "odoo.conf" ]; then
        cmd="$cmd -c odoo.conf"
    else
        # Use default parameters
        cmd="$cmd --db_host=localhost --db_port=$POSTGRES_PORT --db_user=$ODOO_USER --db_password=$ODOO_PASSWORD"
        cmd="$cmd --addons-path=./addons,./odoo/addons --xmlrpc-port=$ODOO_PORT"
    fi
    
    print_status "Running command: $cmd"
    print_success "Odoo server starting..."
    print_status "Access Odoo at: http://localhost:$ODOO_PORT"
    print_status "Press Ctrl+C to stop the server"
    
    # Run Odoo
    exec $cmd
}

# Function to show help
show_help() {
    echo "Odoo Project Runner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --setup-only    Only setup dependencies, don't run Odoo"
    echo "  --skip-setup    Skip setup and run Odoo directly"
    echo "  --help          Show this help message"
    echo ""
    echo "This script will:"
    echo "  1. Check and setup PostgreSQL"
    echo "  2. Install Python dependencies"
    echo "  3. Run Odoo server"
    echo ""
}

# Main execution
main() {
    print_status "Odoo Project Runner Starting..."
    
    # Parse arguments
    SETUP_ONLY=false
    SKIP_SETUP=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --setup-only)
                SETUP_ONLY=true
                shift
                ;;
            --skip-setup)
                SKIP_SETUP=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Check if we're in the right directory
    if [ ! -f "odoo-bin" ] && [ ! -d "odoo" ]; then
        print_error "This doesn't appear to be an Odoo project directory"
        print_error "Please run this script from the Odoo project root"
        exit 1
    fi
    
    if [ "$SKIP_SETUP" = false ]; then
        # Setup PostgreSQL
        setup_postgres
        
        # Setup Python environment
        setup_python
        
        # Check configuration
        check_config
        
        print_success "Setup completed successfully!"
    fi
    
    if [ "$SETUP_ONLY" = false ]; then
        # Run Odoo
        run_odoo
    else
        print_success "Setup completed. Run '$0 --skip-setup' to start Odoo."
    fi
}

# Run main function
main "$@"
